/**
 * Generates a meaningful chat title based on the first user message
 */
export function generateChatTitle(firstMessage: string): string {
  // Clean the message
  const cleanMessage = firstMessage.trim();

  // If message is too short, use a default
  if (cleanMessage.length < 3) {
    return "Web3AI Chat";
  }

  // Remove common prefixes (longer prefixes first to avoid partial matches)
  const prefixesToRemove = [
    "i'm looking for",
    "i would like",
    "i'm trying to",
    "guide me through",
    "walk me through",
    "assist with",
    "i want to",
    "i need to",
    "would like",
    "trying to",
    "looking for",
    "can you",
    "could you",
    "help me",
    "how do i",
    "tell me",
    "show me",
    "teach me",
    "guide me",
    "help with",
    "what is",
    "what are",
    "how to",
    "can we",
    "let's",
    "please",
    "hello",
    "i am",
    "i'm",
    "lets",
    "hey",
    "hi",
    "explain",
  ];

  let title = cleanMessage.toLowerCase();

  // Remove prefixes
  for (const prefix of prefixesToRemove) {
    if (title.startsWith(prefix)) {
      title = title.substring(prefix.length).trim();
      break;
    }
  }

  // If title is empty after removing prefix, use original
  if (title.length === 0) {
    title = cleanMessage;
  }

  // Capitalize first letter
  title = title.charAt(0).toUpperCase() + title.slice(1);

  // Truncate to reasonable length
  const maxLength = 50;
  if (title.length > maxLength) {
    title = title.substring(0, maxLength).trim();
    // Try to end at a word boundary
    const lastSpace = title.lastIndexOf(" ");
    if (lastSpace > maxLength * 0.7) {
      title = title.substring(0, lastSpace);
    }
    title += "...";
  }

  // Remove trailing punctuation except periods and question marks
  title = title.replace(/[,;:!]+$/, "");

  // Ensure it ends properly
  if (!title.match(/[.?]$/)) {
    // Don't add period if it's a question-like title
    if (
      title.toLowerCase().includes("how") ||
      title.toLowerCase().includes("what") ||
      title.toLowerCase().includes("why") ||
      title.toLowerCase().includes("when") ||
      title.toLowerCase().includes("where")
    ) {
      // Keep as is for questions
    } else {
      // Add period for statements
      title += "";
    }
  }

  return title;
}

/**
 * Generates titles for common Web3/blockchain queries
 */
export function generateWeb3ChatTitle(message: string): string {
  const lowerMessage = message.toLowerCase();

  // Web3 specific patterns
  const web3Patterns = [
    { pattern: /swap.*token|token.*swap/i, title: "Token Swap" },
    { pattern: /check.*balance|balance.*check/i, title: "Check Balance" },
    { pattern: /send.*eth|transfer.*eth|eth.*transfer/i, title: "Send ETH" },
    { pattern: /deploy.*contract|contract.*deploy/i, title: "Deploy Contract" },
    { pattern: /mint.*nft|nft.*mint/i, title: "Mint NFT" },
    { pattern: /stak.*token|token.*stak/i, title: "Stake Tokens" },
    { pattern: /bridge.*token|token.*bridge/i, title: "Bridge Tokens" },
    { pattern: /gas.*fee|fee.*gas|gas.*price/i, title: "Gas Fees" },
    {
      pattern: /defi.*protocol|protocol.*defi|understand.*defi|defi/i,
      title: "DeFi Protocol",
    },
    { pattern: /yield.*farm|farm.*yield/i, title: "Yield Farming" },
    { pattern: /liquidity.*pool|pool.*liquidity/i, title: "Liquidity Pool" },
    { pattern: /smart.*contract|contract.*smart/i, title: "Smart Contract" },
    { pattern: /metamask|meta.*mask/i, title: "MetaMask Help" },
    { pattern: /wallet.*connect|connect.*wallet/i, title: "Wallet Connection" },
    {
      pattern: /transaction.*fail|fail.*transaction/i,
      title: "Transaction Failed",
    },
    { pattern: /approve.*token|token.*approv/i, title: "Token Approval" },
    { pattern: /create.*token|token.*creat/i, title: "Create Token" },
    { pattern: /erc.*20|erc20/i, title: "ERC-20 Token" },
    { pattern: /erc.*721|erc721/i, title: "ERC-721 NFT" },
    { pattern: /erc.*1155|erc1155/i, title: "ERC-1155 Token" },
    {
      pattern: /buy.*crypto|crypto.*buy|purchase.*crypto/i,
      title: "Buy Cryptocurrency",
    },
    { pattern: /sell.*crypto|crypto.*sell/i, title: "Sell Cryptocurrency" },
    {
      pattern: /audit.*contract|contract.*audit/i,
      title: "Smart Contract Audit",
    },
    { pattern: /security.*audit|audit.*security/i, title: "Security Audit" },
    { pattern: /polygon|matic/i, title: "Polygon Network" },
    { pattern: /ethereum|eth\b/i, title: "Ethereum" },
    { pattern: /binance.*smart.*chain|bsc/i, title: "Binance Smart Chain" },
    { pattern: /arbitrum/i, title: "Arbitrum" },
    { pattern: /optimism/i, title: "Optimism" },
    { pattern: /avalanche|avax/i, title: "Avalanche" },
    { pattern: /solana|sol\b/i, title: "Solana" },
    { pattern: /uniswap/i, title: "Uniswap" },
    { pattern: /pancakeswap/i, title: "PancakeSwap" },
    { pattern: /opensea/i, title: "OpenSea" },
    { pattern: /web3.*wallet|wallet.*web3/i, title: "Web3 Wallet" },
    { pattern: /dapp.*develop|develop.*dapp/i, title: "DApp Development" },
    {
      pattern: /blockchain.*develop|develop.*blockchain/i,
      title: "Blockchain Development",
    },
    { pattern: /crypto.*price|price.*crypto/i, title: "Crypto Prices" },
    { pattern: /market.*cap|marketcap/i, title: "Market Cap" },
    { pattern: /trading.*bot|bot.*trading/i, title: "Trading Bot" },
    { pattern: /flash.*loan|loan.*flash/i, title: "Flash Loan" },
    { pattern: /dao\b|decentralized.*autonomous/i, title: "DAO" },
    {
      pattern: /governance.*token|token.*governance/i,
      title: "Governance Token",
    },
    { pattern: /stablecoin|stable.*coin/i, title: "Stablecoin" },
    { pattern: /usdc|usdt|dai|busd/i, title: "Stablecoin" },
    { pattern: /layer.*2|l2\b/i, title: "Layer 2" },
    { pattern: /cross.*chain|chain.*bridge/i, title: "Cross-Chain" },
    { pattern: /multi.*sig|multisig/i, title: "Multi-Signature Wallet" },
    { pattern: /cold.*wallet|hardware.*wallet/i, title: "Cold Storage" },
    { pattern: /hot.*wallet|software.*wallet/i, title: "Hot Wallet" },
    { pattern: /private.*key|seed.*phrase/i, title: "Wallet Security" },
  ];

  // Check for Web3 patterns first
  for (const { pattern, title } of web3Patterns) {
    if (pattern.test(message)) {
      return title;
    }
  }

  // Fall back to general title generation
  return generateChatTitle(message);
}
